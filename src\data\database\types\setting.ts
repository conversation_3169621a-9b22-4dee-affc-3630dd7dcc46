/**
 * 设定类型定义
 */

// 设定数据结构
export interface Setting {
  id?: number;
  title: string;
  content: string;
  category: string; // 设定分类，如：世界观、人物关系、背景设定等
  order: number;
  workId: number;
  createdAt: Date;
  updatedAt: Date;
}

// 设定表单数据
export interface SettingFormData {
  title: string;
  content: string;
  category: string;
}

// 设定分类常量
export const SETTING_CATEGORIES = {
  WORLDVIEW: 'worldview',
  CHARACTER_RELATION: 'character_relation',
  BACKGROUND: 'background',
  RULES: 'rules',
  OTHER: 'other'
} as const;

// 设定分类标签
export const SETTING_CATEGORY_LABELS = {
  [SETTING_CATEGORIES.WORLDVIEW]: '世界观',
  [SETTING_CATEGORIES.CHARACTER_RELATION]: '人物关系',
  [SETTING_CATEGORIES.BACKGROUND]: '背景设定',
  [SETTING_CATEGORIES.RULES]: '规则设定',
  [SETTING_CATEGORIES.OTHER]: '其他'
} as const;

// 设定分类类型
export type SettingCategory = typeof SETTING_CATEGORIES[keyof typeof SETTING_CATEGORIES];
