/**
 * 角色仓库
 */
import { Character } from '../types/character';
import { dbOperations } from '../core/operations';
import { DB_CONFIG } from '../config';

const { MAIN } = DB_CONFIG.NAMES;
const { CHARACTERS } = DB_CONFIG.STORES.MAIN;

/**
 * 添加角色
 * @param character 角色
 * @returns 添加后的角色
 */
export const addCharacter = async (character: Omit<Character, 'id'>): Promise<Character> => {
  return dbOperations.add<Character>(MAIN, CHARACTERS, character);
};

/**
 * 获取所有角色
 * @returns 所有角色
 */
export const getAllCharacters = async (): Promise<Character[]> => {
  const characters = await dbOperations.getAll<Character>(MAIN, CHARACTERS);
  // 按创建日期排序，最新的在前面
  return characters.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
};

/**
 * 根据作品ID获取角色
 * @param workId 作品ID
 * @returns 该作品的所有角色
 */
export const getCharactersByWorkId = async (workId: number): Promise<Character[]> => {
  const allCharacters = await getAllCharacters();
  return allCharacters.filter(character => character.workId === workId);
};

/**
 * 根据ID获取角色
 * @param id 角色ID
 * @returns 角色或null
 */
export const getCharacterById = async (id: number): Promise<Character | null> => {
  const character = await dbOperations.getById<Character>(MAIN, CHARACTERS, id);
  return character || null;
};

/**
 * 更新角色
 * @param character 角色
 * @returns 更新后的角色
 */
export const updateCharacter = async (character: Character): Promise<Character> => {
  if (!character.id) throw new Error('Character ID is required');
  return dbOperations.update<Character & { id: number }>(MAIN, CHARACTERS, character as Character & { id: number });
};

/**
 * 删除角色
 * @param id 角色ID
 */
export const deleteCharacter = async (id: number): Promise<void> => {
  return dbOperations.remove(MAIN, CHARACTERS, id);
};

/**
 * 清空所有角色
 */
export const clearAllCharacters = async (): Promise<void> => {
  return dbOperations.clear(MAIN, CHARACTERS);
};
