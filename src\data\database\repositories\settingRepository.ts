/**
 * 设定仓库
 */
import { Setting } from '../types/setting';
import { dbOperations } from '../core/operations';
import { DB_CONFIG } from '../config';

const { MAIN } = DB_CONFIG.NAMES;
const { SETTINGS_DATA } = DB_CONFIG.STORES.MAIN;

/**
 * 添加设定
 * @param setting 设定
 * @returns 添加后的设定
 */
export const addSetting = async (setting: Omit<Setting, 'id'>): Promise<Setting> => {
  return dbOperations.add<Setting>(MAIN, SETTINGS_DATA, setting);
};

/**
 * 获取所有设定
 * @returns 所有设定
 */
export const getAllSettings = async (): Promise<Setting[]> => {
  const settings = await dbOperations.getAll<Setting>(MAIN, SETTINGS_DATA);
  // 按order排序
  return settings.sort((a, b) => a.order - b.order);
};

/**
 * 根据作品ID获取设定
 * @param workId 作品ID
 * @returns 该作品的所有设定
 */
export const getSettingsByWorkId = async (workId: number): Promise<Setting[]> => {
  const allSettings = await getAllSettings();
  return allSettings.filter(setting => setting.workId === workId);
};

/**
 * 根据ID获取设定
 * @param id 设定ID
 * @returns 设定或null
 */
export const getSettingById = async (id: number): Promise<Setting | null> => {
  const setting = await dbOperations.getById<Setting>(MAIN, SETTINGS_DATA, id);
  return setting || null;
};

/**
 * 更新设定
 * @param setting 设定
 * @returns 更新后的设定
 */
export const updateSetting = async (setting: Setting): Promise<Setting> => {
  if (!setting.id) throw new Error('Setting ID is required');
  return dbOperations.update<Setting & { id: number }>(MAIN, SETTINGS_DATA, setting as Setting & { id: number });
};

/**
 * 删除设定
 * @param id 设定ID
 */
export const deleteSetting = async (id: number): Promise<void> => {
  return dbOperations.remove(MAIN, SETTINGS_DATA, id);
};

/**
 * 清空所有设定
 */
export const clearAllSettings = async (): Promise<void> => {
  return dbOperations.clear(MAIN, SETTINGS_DATA);
};
