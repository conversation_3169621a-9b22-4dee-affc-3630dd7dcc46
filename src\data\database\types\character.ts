/**
 * 角色类型定义
 */

// 角色性别类型
export type CharacterGender = '男' | '女' | '无';

// 角色数据结构
export interface Character {
  id?: number;
  name: string;
  gender: CharacterGender;
  personality: string;
  background: string;
  workId: number;
  createdAt: Date;
  updatedAt: Date;
}

// 角色表单数据
export interface CharacterFormData {
  name: string;
  gender: CharacterGender;
  personality: string;
  background: string;
}
