/**
 * 大纲仓库
 */
import { Outline } from '../types/outline';
import { dbOperations } from '../core/operations';
import { DB_CONFIG } from '../config';

const { MAIN } = DB_CONFIG.NAMES;
const { OUTLINES } = DB_CONFIG.STORES.MAIN;

/**
 * 添加大纲
 * @param outline 大纲
 * @returns 添加后的大纲
 */
export const addOutline = async (outline: Omit<Outline, 'id'>): Promise<Outline> => {
  return dbOperations.add<Outline>(MAIN, OUTLINES, outline);
};

/**
 * 获取所有大纲
 * @returns 所有大纲
 */
export const getAllOutlines = async (): Promise<Outline[]> => {
  const outlines = await dbOperations.getAll<Outline>(MAIN, OUTLINES);
  // 按order排序
  return outlines.sort((a, b) => a.order - b.order);
};

/**
 * 根据作品ID获取大纲
 * @param workId 作品ID
 * @returns 该作品的所有大纲
 */
export const getOutlinesByWorkId = async (workId: number): Promise<Outline[]> => {
  const allOutlines = await getAllOutlines();
  return allOutlines.filter(outline => outline.workId === workId);
};

/**
 * 根据ID获取大纲
 * @param id 大纲ID
 * @returns 大纲或null
 */
export const getOutlineById = async (id: number): Promise<Outline | null> => {
  const outline = await dbOperations.getById<Outline>(MAIN, OUTLINES, id);
  return outline || null;
};

/**
 * 更新大纲
 * @param outline 大纲
 * @returns 更新后的大纲
 */
export const updateOutline = async (outline: Outline): Promise<Outline> => {
  if (!outline.id) throw new Error('Outline ID is required');
  return dbOperations.update<Outline & { id: number }>(MAIN, OUTLINES, outline as Outline & { id: number });
};

/**
 * 删除大纲
 * @param id 大纲ID
 */
export const deleteOutline = async (id: number): Promise<void> => {
  return dbOperations.remove(MAIN, OUTLINES, id);
};

/**
 * 清空所有大纲
 */
export const clearAllOutlines = async (): Promise<void> => {
  return dbOperations.clear(MAIN, OUTLINES);
};
