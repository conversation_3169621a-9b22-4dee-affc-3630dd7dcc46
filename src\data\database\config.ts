/**
 * 数据库配置
 */

// 数据库配置类型
export interface DBConfig {
  NAMES: {
    MAIN: string;
    SETTINGS: string;
    NAVIGATION: string;
  };
  VERSIONS: {
    MAIN: number;
    SETTINGS: number;
    NAVIGATION: number;
  };
  STORES: {
    MAIN: {
      TODOS: string;
      WORKS: string;
      KNOWLEDGE: string;
      USER_PROMPT_SELECTIONS: string;
      CHARACTERS: string;
      OUTLINES: string;
      SETTINGS_DATA: string;
    };
    SETTINGS: {
      SETTINGS: string;
    };
    NAVIGATION: {
      STATE: string;
    };
  };
  KEYS: {
    FIRST_VISIT: string;
  };
}

// 数据库配置常量
export const DB_CONFIG: DBConfig = {
  // 数据库名称
  NAMES: {
    MAIN: 'zhuguang_writing_app',
    SETTINGS: 'zhuguang_settings',
    NAVIGATION: 'zhuguang_navigation'
  },
  // 数据库版本
  VERSIONS: {
    MAIN: 9,
    SETTINGS: 7,
    NAVIGATION: 7
  },
  // 存储对象名称
  STORES: {
    // 主数据库存储
    MAIN: {
      TODOS: 'todos',
      WORKS: 'works',
      KNOWLEDGE: 'knowledge',
      USER_PROMPT_SELECTIONS: 'user_prompt_selections',
      CHARACTERS: 'characters',
      OUTLINES: 'outlines',
      SETTINGS_DATA: 'settings_data'
    },
    // 设置数据库存储
    SETTINGS: {
      SETTINGS: 'settings'
    },
    // 导航数据库存储
    NAVIGATION: {
      STATE: 'state'
    }
  },
  // 存储键
  KEYS: {
    FIRST_VISIT: 'zhuguang_first_visit'
  }
};
